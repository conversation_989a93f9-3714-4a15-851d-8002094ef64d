{"name": "mern-blog", "version": "1.0.0", "description": "", "packageManager": "pnpm@10.17.1", "workspaces": ["./apps/**", "./packages/**"], "scripts": {"dev": "turbo dev", "dev:web": "turbo dev --filter=web", "dev:backend": "turbo dev --filter=backend", "build:web": "turbo build --filter=web", "build:backend": "turbo build --filter=backend", "format": "prettier . --write", "check-formating": "prettier . -c", "check-types": "tsc --noEmit"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "type": "module", "devDependencies": {"@types/node": "^24.5.2", "prettier": "^3.6.2", "turbo": "^2.5.8", "typescript": "^5.9.2"}}