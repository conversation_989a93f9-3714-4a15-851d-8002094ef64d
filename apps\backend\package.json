{"name": "backend", "version": "1.0.0", "description": "", "main": "./src/app.ts", "scripts": {"build": "tsc", "dev": "nodemon --ext ts --exec \"tsc && node ./dist/app.js\""}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "type": "module", "devDependencies": {"@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.5.2", "concurrently": "^9.2.1", "nodemon": "^3.1.10", "typescript": "^5.9.2"}, "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "helmet": "^8.1.0", "mongoose": "^8.18.2", "zod": "^4.1.11"}}