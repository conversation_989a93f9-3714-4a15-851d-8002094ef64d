import { Schema, model } from "mongoose";

export const Post = model(
  "Post",
  new Schema(
    {
      title: {
        type: String,
        required: true,
        unique: true
      },

      description: {
        type: String,
        required: true
      },

      content: {
        type: String,
        required: true
      },

      image: {
        type: String,
        required: true
      },

      likes: {
        type: Number,
        required: true
      }
    },

    {
      timestamps: true
    }
  )
);
