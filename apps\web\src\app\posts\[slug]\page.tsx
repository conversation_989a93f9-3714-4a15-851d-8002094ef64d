"use client";

import { getAPIUrl } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useParams, useRouter } from "next/navigation";
import { ArrowLeft, Heart } from "lucide-react";

interface Post {
  _id: string;
  title: string;
  description: string;
  content: string;
  image: string;
  likes: number;
  createdAt: string;
  updatedAt: string;
}

export default function PostPage() {
  const params = useParams();
  const router = useRouter();
  const postSlug = params.slug as string;

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["post", postSlug],
    queryFn: async () => {
      const response = await axios.get(getAPIUrl(`posts/${postSlug}`));
      return response.data;
    },
    enabled: !!postSlug
  });

  const post: Post | undefined = data?.post;

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-300 rounded mb-6"></div>
          <div className="h-8 bg-gray-300 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-6"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <p className="text-red-600 font-medium">Error loading post</p>
          <p className="text-red-500 text-sm mt-2">
            {error?.message || "Failed to load post"}
          </p>
          <button
            onClick={() => router.push("/")}
            className="mt-4 inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg mb-4">Post not found</p>
          <button
            onClick={() => router.push("/")}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Back button */}
      <button
        onClick={() => router.push("/")}
        className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6 transition-colors"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Posts
      </button>

      {/* Post content */}
      <article className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        {/* Post image */}
        <div className="aspect-video w-full">
          <img
            src={post.image}
            alt={post.title}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Post content */}
        <div className="p-8">
          {/* Title */}
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            {post.title}
          </h1>

          {/* Meta information */}
          <div className="flex items-center justify-between mb-6 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span>
                Published: {new Date(post.createdAt).toLocaleDateString()}
              </span>
              {post.updatedAt !== post.createdAt && (
                <span>
                  Updated: {new Date(post.updatedAt).toLocaleDateString()}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Heart className="w-4 h-4 text-red-500" />
              <span>{post.likes} likes</span>
            </div>
          </div>

          {/* Description */}
          <div className="text-lg text-gray-700 dark:text-gray-300 mb-6 font-medium">
            {post.description}
          </div>

          {/* Content */}
          <div className="prose prose-lg max-w-none dark:prose-invert">
            <div className="whitespace-pre-wrap text-gray-800 dark:text-gray-200 leading-relaxed">
              {post.content}
            </div>
          </div>
        </div>
      </article>
    </div>
  );
}
