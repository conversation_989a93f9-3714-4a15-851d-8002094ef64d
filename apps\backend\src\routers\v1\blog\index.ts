import { Router } from "express";
import z, { ZodError } from "zod";
import { connect } from "../../../database/connect.js";
import { Post } from "../../../models/Post.class.js";

const router: Router = Router();

router.get("/posts", async (req, res) => {
  try {
    const db = await connect();

    if (!db) throw new Error("Database connection was unsuccessful");

    const { page, limit, search } = z
      .object({
        search: z.string().optional().default(""),
        page: z.string().transform(Number).pipe(z.number().min(1)).default(1),
        limit: z
          .string()
          .transform(Number)
          .pipe(z.number().min(1).max(50))
          .default(5)
      })
      .parse(req.query);

    const searchFilter = search
      ? {
          $or: [
            { title: { $regex: search, $options: "i" } },
            { description: { $regex: search, $options: "i" } }
          ]
        }
      : {};

    const posts = await Post.find()
      .limit(limit)
      .where(searchFilter)
      .skip((page - 1) * limit);

    res.json({
      success: true,
      posts
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.json({
        error: true,
        details: error.issues
      });
      return;
    }

    res.json({
      posts: [],
      error: true
    });
  }
});

export { router };
