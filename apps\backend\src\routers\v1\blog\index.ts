import { Router } from "express";
import z, { ZodError } from "zod";
import { connect } from "../../../database/connect.js";
import { Post } from "../../../models/Post.class.js";

const router: Router = Router();

// Get all posts with pagination and search
router.get("/posts", async (req, res) => {
  try {
    const db = await connect();

    if (!db) throw new Error("Database connection was unsuccessful");

    const { page, limit, search } = z
      .object({
        search: z.string().optional().default(""),
        page: z.string().transform(Number).pipe(z.number().min(1)).default(1),
        limit: z
          .string()
          .transform(Number)
          .pipe(z.number().min(1).max(50))
          .default(5)
      })
      .parse(req.query);

    const searchFilter = search
      ? {
          $or: [
            { title: { $regex: search, $options: "i" } },
            { description: { $regex: search, $options: "i" } }
          ]
        }
      : {};

    const posts = await Post.find()
      .limit(limit)
      .where(searchFilter)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const totalPosts = await Post.countDocuments(searchFilter);

    res.json({
      success: true,
      posts,
      pagination: {
        page,
        limit,
        total: totalPosts,
        totalPages: Math.ceil(totalPosts / limit)
      }
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        error: true,
        message: "Validation error",
        details: error.issues
      });
      return;
    }

    res.status(500).json({
      error: true,
      message: "Failed to fetch posts"
    });
  }
});

// Get single post by ID, title, or slug
router.get("/posts/:identifier", async (req, res) => {
  try {
    const db = await connect();

    if (!db) throw new Error("Database connection was unsuccessful");

    const { identifier } = z
      .object({
        identifier: z.string().min(1, "Post identifier is required")
      })
      .parse(req.params);

    let post = null;

    // First try to find by MongoDB ObjectId
    if (identifier.match(/^[0-9a-fA-F]{24}$/)) {
      post = await Post.findById(identifier);
    }

    // If not found by ID, try to find by exact title match
    if (!post) {
      post = await Post.findOne({ title: identifier });
    }

    // If still not found, try to find by title with case-insensitive search
    if (!post) {
      post = await Post.findOne({
        title: {
          $regex: new RegExp(
            `^${identifier.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}$`,
            "i"
          )
        }
      });
    }

    // If still not found, try to find by slugged title (convert spaces to hyphens and vice versa)
    if (!post) {
      const slugToTitle = identifier.replace(/-/g, " ");
      const titleToSlug = identifier.replace(/\s+/g, "-");

      post = await Post.findOne({
        $or: [
          {
            title: {
              $regex: new RegExp(
                `^${slugToTitle.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}$`,
                "i"
              )
            }
          },
          {
            title: {
              $regex: new RegExp(
                `^${titleToSlug.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}$`,
                "i"
              )
            }
          }
        ]
      });
    }

    // If still not found, try partial title match
    if (!post) {
      post = await Post.findOne({
        title: {
          $regex: identifier.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
          $options: "i"
        }
      });
    }

    if (!post) {
      res.status(404).json({
        error: true,
        message: "Post not found"
      });
      return;
    }

    res.json({
      success: true,
      post
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        error: true,
        message: "Invalid post identifier",
        details: error.issues
      });
      return;
    }

    res.status(500).json({
      error: true,
      message: "Failed to fetch post"
    });
  }
});

// Create new post
router.post("/posts", async (req, res) => {
  try {
    const db = await connect();

    if (!db) throw new Error("Database connection was unsuccessful");

    const postData = z
      .object({
        title: z
          .string()
          .min(1, "Title is required")
          .max(200, "Title too long"),
        description: z
          .string()
          .min(1, "Description is required")
          .max(500, "Description too long"),
        content: z.string().min(1, "Content is required"),
        image: z.string().url("Invalid image URL"),
        likes: z.number().min(0).default(0)
      })
      .parse(req.body);

    const post = new Post(postData);
    await post.save();

    res.status(201).json({
      success: true,
      message: "Post created successfully",
      post
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        error: true,
        message: "Validation error",
        details: error.issues
      });
      return;
    }

    // Handle duplicate title error
    if (
      error &&
      typeof error === "object" &&
      "code" in error &&
      error.code === 11000
    ) {
      res.status(400).json({
        error: true,
        message: "A post with this title already exists"
      });
      return;
    }

    res.status(500).json({
      error: true,
      message: "Failed to create post"
    });
  }
});

// Update existing post
router.put("/posts/:id", async (req, res) => {
  try {
    const db = await connect();

    if (!db) throw new Error("Database connection was unsuccessful");

    const { id } = z
      .object({
        id: z.string().min(1, "Post ID is required")
      })
      .parse(req.params);

    const updateData = z
      .object({
        title: z
          .string()
          .min(1, "Title is required")
          .max(200, "Title too long")
          .optional(),
        description: z
          .string()
          .min(1, "Description is required")
          .max(500, "Description too long")
          .optional(),
        content: z.string().min(1, "Content is required").optional(),
        image: z.string().min(1, "Image URL is required").optional(),
        likes: z.number().min(0).optional()
      })
      .parse(req.body);

    const post = await Post.findByIdAndUpdate(id, updateData, {
      new: true,
      runValidators: true
    });

    if (!post) {
      res.status(404).json({
        error: true,
        message: "Post not found"
      });
      return;
    }

    res.json({
      success: true,
      message: "Post updated successfully",
      post
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        error: true,
        message: "Validation error",
        details: error.issues
      });
      return;
    }

    // Handle duplicate title error
    if (
      error &&
      typeof error === "object" &&
      "code" in error &&
      error.code === 11000
    ) {
      res.status(400).json({
        error: true,
        message: "A post with this title already exists"
      });
      return;
    }

    res.status(500).json({
      error: true,
      message: "Failed to update post"
    });
  }
});

// Delete post
router.delete("/posts/:id", async (req, res) => {
  try {
    const db = await connect();

    if (!db) throw new Error("Database connection was unsuccessful");

    const { id } = z
      .object({
        id: z.string().min(1, "Post ID is required")
      })
      .parse(req.params);

    const post = await Post.findByIdAndDelete(id);

    if (!post) {
      res.status(404).json({
        error: true,
        message: "Post not found"
      });
      return;
    }

    res.json({
      success: true,
      message: "Post deleted successfully"
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        error: true,
        message: "Invalid post ID",
        details: error.issues
      });
      return;
    }

    res.status(500).json({
      error: true,
      message: "Failed to delete post"
    });
  }
});

// Like/Unlike post
router.put("/posts/:id/like", async (req, res) => {
  try {
    const db = await connect();

    if (!db) throw new Error("Database connection was unsuccessful");

    const { id } = z
      .object({
        id: z.string().min(1, "Post ID is required")
      })
      .parse(req.params);

    const { action } = z
      .object({
        action: z.enum(["like", "unlike"])
      })
      .parse(req.body);

    const increment = action === "like" ? 1 : -1;

    const post = await Post.findByIdAndUpdate(
      id,
      { $inc: { likes: increment } },
      { new: true }
    );

    if (!post) {
      res.status(404).json({
        error: true,
        message: "Post not found"
      });
      return;
    }

    // Ensure likes don't go below 0
    if (post.likes < 0) {
      post.likes = 0;
      await post.save();
    }

    res.json({
      success: true,
      message: `Post ${action}d successfully`,
      post
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        error: true,
        message: "Validation error",
        details: error.issues
      });
      return;
    }

    res.status(500).json({
      error: true,
      message: "Failed to update post likes"
    });
  }
});

export { router };
