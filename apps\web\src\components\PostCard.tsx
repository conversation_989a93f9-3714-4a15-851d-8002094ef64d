interface Post {
  _id: string;
  title: string;
  description: string;
  content: string;
  image: string;
  likes: number;
  createdAt: string;
}

interface PostCardProps {
  post: Post;
}

export default function PostCard({ post }: PostCardProps) {
  return (
    <div className="text-muted-foreground">
      <img
        src={post.image}
        alt={post.title}
        className="w-full h-48 object-cover"
      />
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-2">{post.title}</h2>
        <p className="mb-4 line-clamp-3 text-muted-foreground">
          {post.description}
        </p>
        <div className="flex items-center justify-between">
          <span className="text-sm">
            {new Date(post.createdAt).toLocaleDateString()}
          </span>
          <div className="flex items-center space-x-4">
            <span className="flex items-center text-sm">❤️ {post.likes}</span>
            <button className="text-blue-600 hover:text-blue-800 font-medium">
              Read More
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
