"use client";

import PostCard from "@/components/PostCard";
import { getAPIUrl } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

export default function Home() {
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["fetch-posts"],
    queryFn: async () => {
      return (await axios.get(getAPIUrl("posts"))).data;
    }
  });

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center mb-12 text-foreground">
        <h1 className="text-4xl font-bold mb-4">Welcome to MERN Blog</h1>
        <p className="text-xl">Discover amazing stories and insights</p>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="ml-4">Loading posts...</p>
        </div>
      )}

      {isError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <p className="text-red-600 font-medium">Error loading posts</p>
          <p className="text-red-500 text-sm mt-2">{error?.message}</p>
        </div>
      )}

      {!isLoading && !isError && data?.posts && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {data.posts.map((post: any) => (
            <PostCard key={post._id} post={post} />
          ))}
        </div>
      )}

      {!isLoading && !isError && (!data?.posts || data.posts.length === 0) && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No posts found</p>
        </div>
      )}
    </div>
  );
}
