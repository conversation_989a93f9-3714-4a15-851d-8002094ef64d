import Footer from "@/components/Footer";
import Header from "@/components/Header";
import ReactQueryProvider from "@/providers/react-query";
import type { Metadata } from "next";
import { Source_Sans_3, Source_Serif_4 } from "next/font/google";
import "./globals.css";

const source_serif_4 = Source_Serif_4({
  subsets: ["latin"],
  weight: ["500"],
  variable: "--font-serif"
});

const source_sans_3 = Source_Sans_3({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-sans"
});

export const metadata: Metadata = {
  title: "MERN Blog",
  description: "A modern blog built with MERN stack"
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" style={{ colorScheme: "dark" }}>
      <body
        className={`${source_serif_4.variable} ${source_sans_3.variable} antialiased min-h-screen flex flex-col dark`}
      >
        <ReactQueryProvider>
          <Header />
          {children}
          <Footer />
        </ReactQueryProvider>
      </body>
    </html>
  );
}
