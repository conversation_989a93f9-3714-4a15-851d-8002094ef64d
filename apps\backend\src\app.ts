import bodyParser from "body-parser";
import cors from "cors";
import express, { type Request, type Response } from "express";
import helmet from "helmet";
import "./env.js";
import { routers as v1 } from "./routers/v1/index.js";

const app = express();
const port = 4000;

app.use(helmet());
app.use(cors());
app.use(bodyParser.json());

app.use("/v1/", v1);

app.get("/", (_req: Request, res: Response) => {
  res.json({
    success: true,
    message: "Hi, this is a simple blog built using MERN Stack"
  });
});

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
